import {
    _decorator,
    Component,
    Node,
    tween,
    v3,
    Label,
    Button,
    Vec3,
    sp,
    Prefab,
    instantiate,
} from 'cc'
import { CommonDialog } from '../CommonDialog/CommonDialog'
import { cat } from '@/core/manager'
import { GameEventConstant } from '@/core/business/constant'
import { sleep } from '@/core/business/util/TimeUtils'
import store from '@/core/business/store'
import { CardTheme } from '../../components/card/CardTheme'
import { plistImage } from '@/core/components/plist/plistImage'

const { ccclass, property } = _decorator

export interface ConfirmThemeDialogProps {
    theme_position: Vec3 //主题卡牌动画的目的位置
}

@ccclass('ConfirmThemeDialog')
export class ConfirmThemeDialog extends CommonDialog<ConfirmThemeDialogProps> {
    @property({ type: Node, tooltip: '内容容器' })
    content: Node = null!

    @property({ type: plistImage, tooltip: 'plist动画组件1' })
    plistImageComp: plistImage = null!

    @property({ type: plistImage, tooltip: 'plist动画组件2' })
    plistImageComp2: plistImage = null!

    @property({ type: Prefab, tooltip: '主题卡牌预制体' })
    card_theme_prefab: Prefab = null!

    override props: ConfirmThemeDialogProps = {
        theme_position: v3(0, 0, 0),
    }

    protected override initUI(): void {
        super.initUI()
        this.playConfirmThemeAnimation()
    }

    /**
     * 停止动画播放
     */
    public stopAnimation(): void {
        if (this.plistImageComp) {
            this.plistImageComp.stop()
        }

        if (this.plistImageComp2) {
            this.plistImageComp2.stop()
        }
    }

    protected override onDestroy(): void {
        this.stopAnimation()
        super.onDestroy?.()
    }
    private playConfirmThemeAnimation(): void {
        this.plistImageComp.setCompleteListener(() => {
            this.plistImageComp.node.active = false
            this.plistImageComp2.node.active = true
            this.plistImageComp2.play()
            this.showCardTheme()
        })
    }

    private async showCardTheme() {
        window.ccLog('ConfirmThemeDialog-->showCardTheme')
        const card_theme_node = instantiate(this.card_theme_prefab)
        card_theme_node
            .getComponent(CardTheme)!
            .setPosition(v3(0, 100, 0))
            .setScale(v3(0, 0, 0))
            .addToParent(this.content)

        tween(card_theme_node)
            .to(0.5, {
                scale: v3(1, 1, 1),
            })
            .call(() => {
                this.stopAnimation()
                this.plistImageComp2.node.active = false
            })
            .start()

        await sleep(1000)

        if (!this.props?.theme_position) {
            console.error('[ConfirmThemeDialog] 主题卡牌目的位置不存在')
            return
        }

        const { x, y } = this.props.theme_position

        const tweenPromise: Promise<void>[] = []
        tweenPromise.push(
            new Promise((resolve) => {
                tween(card_theme_node)
                    .to(0.5, {
                        position: v3(x, y, 0),
                        scale: v3(0, 0, 0),
                    })
                    .call(() => {
                        card_theme_node.removeFromParent()
                        card_theme_node.destroy()
                        cat.event.dispatchEvent(
                            GameEventConstant.SHOW_STORY_THEME
                        )
                        resolve()
                    })
                    .start()
            })
        )

        await Promise.all(tweenPromise)
    }
}
