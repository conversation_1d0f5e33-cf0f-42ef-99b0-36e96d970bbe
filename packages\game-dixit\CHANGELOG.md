# 只言片语 - 更新日志

本项目的所有重要更改都将记录在此文件中。


### [0.1.3](https://gitlab.stnts.com/cloudgame/bomb-duck/compare/<EMAIL>-dixit@0.1.3) (2025-05-29)


### 🔧 其他更改

* pb 生成配置调整 ([01b6cd6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/01b6cd6e124ca8c2562a2e3f4d2b36f9fa69f416))


### ✨ 新功能

* 唱票弃权调整 ([e47be83](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e47be833245248c18135db1878ed0b4a4fbf2304))
* 唱票弃权调整 ([3366d5c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3366d5c4c7013849d3c29b242085c153386a3372))
* 故事书增加空状态和ai生成状态 ([5ad890e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5ad890e6b8df7b732200286a8dcaede41a16bea5))
* 故事书增加空状态和ai生成状态 ([dbc1d35](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/dbc1d35f8a52c5c370fe81bf074102ff2d60ed36))
* 故事书增加空状态和ai生成状态 ([d399b5c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d399b5ca0257617302c33ca548e6e03f025b9633))
* AI辅助生成故事 ([e1ef20a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e1ef20a64cf202a9e20f0b2126eb035a855b0695))


### 📝 文档更新

* 版本管理文档完善 ([a494edc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a494edc03332abdb8466408818236954a3319298))
* 分享文档更新 ([93e3f03](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/93e3f034051be2a0c59292cc764e5abfa65ee289))
* 分享文档更新 ([3aebd3e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3aebd3e19518e0891190e196d538ce322f25b64f))
* 分享文档更新 ([3bb5639](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3bb5639c66c01058c43e5993491ec5b6ec3ac8a5))
* 分享文档更新 ([e437885](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e4378857c9eeee36471f7746a3c4ede3295a6aa5))
* 分享文档更新 ([1a36f9d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1a36f9d37217104e5f4d4874898c20c90ebb80ca))
* 分享文档更新 ([c120eb9](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c120eb90842664d1961d929da9f9abb0dcd381b7))
* 分享文档更新 ([3c1a266](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3c1a2661149e1fe9a44d8acb56816d8689c4418e))
* 分享文档更新 ([47d60c6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/47d60c68cb754f6c015339250b8ee423d9283b81))
* 文档更新 ([cc19072](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cc19072dc30b2d13015a381c7f88651f72625f26))
* 移除测试文件 ([2b14e5f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2b14e5f2b227618ceef543da0bfff4398c8df917))

### [0.1.2](https://gitlab.stnts.com/cloudgame/bomb-duck/compare/<EMAIL>-dixit@0.1.2) (2025-05-28)


### 📝 文档更新

* 添加测试文档 ([2dcf41e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2dcf41e1061f33c0e66d30a1caad6e9961f335ce))


### ✨ 新功能

* 添加测试文档 ([dfea70c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/dfea70c56fe3cd91ddd6530de156584c444b9199))

### 0.1.1 (2025-05-28)


### ♻️ 代码重构

* 预制件拆分 ([741b496](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/741b496d5346ee40698a9df7ead7ca2dbe00ae4b))
* 预制件拆分 ([bfc8624](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bfc8624be0fff760f500106a9d2418d29b507891))
* 遮罩调整 ([d65ba5e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d65ba5e497677eaf10cdfd7f8e09156077473e57))
* 重构plist相关组件，解决类型报错 ([310e5d1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/310e5d1d0be5ca66f14cafbad2592c74617e84ca))
* CommonDialog 组件统一处理所有弹框组件的关闭 ([34c1f1a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/34c1f1a18c4b043060bd494048131a6066f8f99b))
* CommonDialog 组件统一处理所有弹框组件的关闭 ([8d608f7](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8d608f73682055316086ecd571dd08129e470244))
* WIP: 预制件拆分 ([3a1ff49](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3a1ff49f98485c291a5946c2ffe9683baec9bdc4))
* WIP: 预制件拆分 ([396bd9f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/396bd9f2d7bbfc128fa58af715c574bd4e19fbde))
* WIP: 预制件拆分 ([7ba1d33](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7ba1d33a57f81c01e7c3e4ea512a066105f3845c))
* WIP: 预制件拆分 ([66d175a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/66d175a5661cbbc3455a3c3c54446fda507bca22))
* WIP: 预制件拆分 ([cef3eee](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cef3eee36495fef88fcfd45f10ecd0a8a719d7cb))
* WIP: 预制件拆分 ([1638e6f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1638e6f2a32a9c879a806c4efc44b5c27463ef15))
* WIP: 预制件拆分 ([a18fb90](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a18fb908b8d322d889151b755e4d5ab6aa92c396))


### 📦 构建系统

* 纹理压缩配置调整 ([fc73aaf](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fc73aaf3aa50078dd45cf8366f59f859db5845a1))


### 🔧 其他更改

* standard-version ([a3c864b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a3c864bb8cab6ff6be3e855c57fca72fbc811a0f))
* upgrade pb ([a0adbba](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a0adbbaf50db29ed71d433d960ab9da9f3d7fcbd))
* upgrade pb ([0fe5d64](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0fe5d64f660eb2096a784f212397433800e1c17c))


### ✨ 新功能

* 背景音音量调整 ([48d6585](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/48d6585aa3df1b6fd8b5b279d5033f408d71f36c))
* 补充jsbridge文档 ([1c9aa92](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1c9aa92ba055b29a59bd924a25a48ec7f971d129))
* 唱票环节音效 ([4ca98d5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4ca98d5d703c9e7bc5c4e4b946843ba40c724527))
* 唱票环节音效 ([94d0c55](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/94d0c55fa68af7dad6e75d97c55451e9c83e9793))
* 唱票无人得分处理 ([553642e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/553642eaeb2cbcc34b19ed5ca7576815f9e63d1b))
* 唱票预制件 ([6088d11](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6088d11852984577eb666dd16cea0a01ebecc9dd))
* 处理单个玩家变更的消息，将玩家数据合并到playerinfo里面 ([0347fde](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0347fde27916402b03736ed5b6b108a582c7dc3a))
* 打开故事书音效调整 ([034e20e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/034e20ee7d01ddb2a20f69328d1a7a9df3cdc0fd))
* 导入音效文件 & 定义 enum ([260e07f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/260e07fae4b7f4296bcc2edcfcdfc6717f7cc89d))
* 点击打破托管 ([bf80940](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bf809405c5498cd38fe64ff490bc96ea1228e99d))
* 动态注册 rpc 的方法 & 定义方法类型 & 投票请求调用 vote 方法 ([cfee25b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cfee25b03d4db62dacb813389338b995bdacbe42))
* 动态注册 rpc 的方法 & 定义方法类型 & 投票请求调用 vote 方法 ([1a860d1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1a860d1235ae8aaac77b8274d79b740792736ce6))
* 对接音效：故事书、投票、卡牌预览 ([57fb2c1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/57fb2c106e9a0471c422cac2b402ba3033092a27))
* 非语音房内给用户增加默认头像 ([f2e2141](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f2e2141e10827cf0bf70c644ba7720332af80c06))
* 构建模板 & 日志模式 ([7932271](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/793227101f1f700cefebb2c4b55ca95fbb68d7dc))
* 故事书-说书人昵称处理 ([31c0762](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/31c07620986b964f9de49c84fa34fe1885a37b3c))
* 故事书切换卡牌模式和故事模式增加渐隐渐现转场效果 ([18d7831](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/18d783173452f6e64a30da0440d2244e54775ec2))
* 故事书优化 ([7ac1183](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7ac118397108cf985e3be15b548731a0d8664806))
* 故事书预制件 ([4b72bfe](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4b72bfee40c89c08b00f8e6d4ab98cf88bc3cfd2))
* 故事书组件 disableTouch 处理 ([8db51b8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8db51b8b5ba39016583f170ccbccf285007dee19))
* 观战-投票环节 ([e32fe95](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e32fe952782bf6fd5c710abc3322c0bd20c8d89a))
* 积分榜预制件 ([1423b8c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1423b8c14231b8239bd00447d967b939615ff727))
* 计分榜和故事书图标点击缩放效果 ([4d5b57b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4d5b57b702a36f45ec27ad89624d8c8f054a21ab))
* 简化 breakHosting 调用 ([867a32d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/867a32da1f26ad9b2cb83f3008e4004008142930))
* 揭示说书人动效：魔法棒+翻牌 ([35c3454](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/35c3454da6606b7c51745b668711aaf0f046b527))
* 进入投票阶段动画 ([dd1acf1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/dd1acf10fbed980b79d28349d973d62c424f30fc))
* 进入投票阶段过场 ([70a5d74](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/70a5d74ed74fc2983845b8eea5d93534be2644a7))
* 卡牌尺寸规格 ([3e14f4d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3e14f4d34c8b04e5cf0f50fb9a18cab83e52bcfd))
* 卡牌放大预览功能 ([5d1ea3e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5d1ea3e812a07a0fc365c509fe897416d85be183))
* 卡牌加载加入重试机制 ([e94de96](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e94de965682fbde54d545874d7c4fa2beafc69f2))
* 卡牌加载优化-动态获取后缀 ([12008f1](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/12008f177c26c8bd4e50dfe9e339ea31b3294c8c))
* 配置每个包独立的版本管理，限制changelog只包含当前包的提交 ([8251041](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8251041e22fcda32bef901f4349e28a0572351bc))
* 手牌对接 ([3ecd066](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3ecd0666a7ec4aa445bd1c330039c9c8e3ef1f27))
* 所有游戏同步性能优化改动 ([e089d40](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e089d40cef2c2f315043995446637fdedb5d99ff))
* 同步框架 ([5d5cbdd](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5d5cbdd4fdb5138143b9bbdd77569d4a0784be6c))
* 同步装扮框架代码 & 海盗桶对接装扮 ([81eae5a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/81eae5ab8bfb35c7a2f146f2862e02480904fd34))
* 同步装扮框架代码 & 海盗桶对接装扮 ([04fa121](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/04fa1215385933a41f67db2f495ef5245808e2a3))
* 同步装扮框架代码 & 海盗桶对接装扮 ([29df5e6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/29df5e6ffb972bb0173724ba5147629d3911b90c))
* 同步装扮框架代码 & 海盗桶对接装扮 ([4b4fee6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4b4fee63e83fa5204a0984b27d6066fac3e157a4))
* 同步core到只言片语 ([663c2e8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/663c2e85dc70d7ccb80c6eef02c084e1d30487b2))
* 头像对接装扮 ([745014a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/745014a104fd4a4cb579b38b0e416497b15af5e5))
* 头像对接装扮 ([4b19f99](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4b19f994ddf1e750beed6f73b8b586311a86dded))
* 头像对接装扮 ([3bb8d07](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3bb8d0712d151544a695adfde184a8fa86700e6b))
* 头像对接装扮 ([df6a931](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/df6a931f69cdd197a4e847626d06f84e21d83896))
* 头像增加弃权 ([9e34ebd](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9e34ebd96ec579e78cbb0a2bd4aa2125e4a2dbc1))
* 投票和玩家标签相关调整 ([03c0e7d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/03c0e7de017d4174fab1a3b2ee4864e27e639ce7))
* 投票和玩家标签相关调整 ([e80a463](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e80a463bae376fc6933cc7d97dc548682e6c4eb2))
* 投票相关调整 ([8639dfb](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8639dfbcd8d2e9f1e06125dc4d750cc3bd230ff5))
* 投票预制件 ([c4b1fff](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c4b1fff5b27f162510567cda8283803209034c65))
* 投票增加卡牌选中状态 ([7092ae0](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7092ae0c137bee3b0cff1eff1752080e00424d01))
* 玩家头像 ([85a65a8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/85a65a8388778e2a441366f34db10ec249dab669))
* 玩家头像 ([2b692a5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2b692a5acbf342eafee7f951d1becbe2ebe2c145))
* 修复观战路由 ([8003f3a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8003f3a54ddd8d518065db57a7e25c65cae23333))
* 用户标号改为颜色标签 & 抽取预制体 ([09b95cb](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/09b95cbc9b8c77627250dd94b03f064e64b5fbfe))
* 用户标号改为颜色标签 & 抽取预制体 ([33e6745](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/33e6745ecf4e5faf223c9632f4c8a6f55f5bca5b))
* 用户头像标识调整 ([af3f2e8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/af3f2e8d80d15367528b33c7788280e4b630e32c))
* 用户头像标识调整 ([a4f758a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a4f758a78714d5a651acd47f2365cd0f90db518d))
* 用户头像装扮 ([8a429b4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8a429b4f75c92ecb70049fbd1aa727fa8d500cae))
* 优化noMask没有弹窗放大动画的问题 ([8e63bde](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8e63bdeb75b27eb3aace4f401f4dd963b9674cca))
* 增加击中非弱点的2d动画 ([a1dd59f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a1dd59f3715ff8da9c72e589f8ae369578a51f2d))
* 增加讲故事弹框预制件 ([6927656](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/692765641ce6e21f9df984562000f1ca79a8ed9b))
* 增加桥接方法同步到各个项目 ([42600b4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/42600b42075b1cb4e71442fb06d389f8c45df2d6))
* 增加遮罩层不透明度 ([712973d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/712973d0ce2642dc272533b875ff0365b7a00c88))
* 自动图集 ([fa48fe4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fa48fe4a951676c3de05c7ec7a6133a1bf883e28))
* CommonDialog 在关闭时增加动画效果 ([ecbfa82](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ecbfa828c8e550da216f15f89ad3f19aea3bff9b))
* main_title 层级问题 ([5d14315](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5d14315bd4fd176476a4d7df3a4fd04a30d48950))
* plistImage 支持添加 .plist 文件资源 ([8cec5b3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8cec5b327446582a4ed307e1060e17f35903ce9b))
* plistImage 组件支持播放本地plist资源，支持设置播放完成回调 ([146b25f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/146b25f86afc630d1a189bd7f352ac5ef04c890c))
* rpc 方法支持传入options ([44ece8c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/44ece8cb89c9217eba18dbba508b1f20354b6fa4))
* store.game.playerList 整合 Player 和 BasePlayer ([9e793cd](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9e793cd0472e42bd9c84d77ec5d895562dd73cd6))
* store.game.playerList 整合 Player 和 BasePlayer ([b919a4f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b919a4fdb6404240893b1dde07c0fe74ab3455e5))
* store.game.playerList 整合 Player 和 BasePlayer ([096f92b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/096f92b91343d83c68dd87c59431557f0f5b9189))
* toast UI & 唱票所有玩家弃权处理 ([8d94d33](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8d94d3377cf7c8a2da8a86c4656d97fd66df6b91))
* vote ([b665eff](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b665eff84ff35da5de80200830996d2f61ad8888))
* W揭示说书人动效：魔法棒+翻牌 ([d092b7e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d092b7e5e87eb24c070146d60d916e8563c1c6db))
* WIP: 唱票 ([3ea0a92](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3ea0a92271afe2b512e6e3372616430f9d9345fc))
* WIP: 唱票 ([ad25d7d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ad25d7d52db0ebba09d4d4efb7b1fc6217ac1216))
* WIP: 唱票 ([5d1b254](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5d1b25441930ed145628a81a7cdf8f7355c9fe27))
* WIP: 唱票 ([fc74b9a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fc74b9a58753a7444f49d7bf6d2cc1b09374eee5))
* WIP: 唱票 ([86af428](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/86af4282287a39e0bd79ddeba5009e3bbad3f1f0))
* WIP: 唱票 ([c0b2b81](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c0b2b81b2b94873621100307367313ab20027723))
* WIP: 唱票 ([9e6b8ec](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9e6b8ec07f4bf9d60ff00976c7c71cb890c11703))
* WIP: 唱票 ([9855b27](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9855b27949fa504d224a71d3ea24d0d2ccbc58a8))
* WIP: 唱票 ([e28f1e6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e28f1e6387b5554e566c1eb19b0f47653d22c31d))
* WIP: 唱票 ([7638cbc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7638cbcea014242d937699a3f3188b67bcc1c336))
* WIP: 唱票 ([df03b11](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/df03b11af204d382ca6553bc28f0ba0b75f22d0e))
* WIP: 唱票 ([b5df69f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b5df69f9a6134070e870f46760acfc53cad6982f))
* WIP: 唱票 ([bc8b412](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bc8b412bd0b70cdc8e2ad9c5de1934c5c095dd2c))
* WIP: 唱票 ([c5291f8](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c5291f82c881f437d43ed585666f3ad7448850f3))
* WIP: 唱票 ([abdec24](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/abdec24da8bfe1c96e7c79b07df1e814f1a94fcc))
* WIP: 唱票 ([9ba777b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9ba777bd7ce33dc66be82494b332708e94044017))
* WIP: 唱票 ([cbb1c5d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/cbb1c5d855834b09c42a91d916d74206be893356))
* WIP: 唱票 & openUI 支持 noMask 参数 ([075cb8c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/075cb8cead3daaf2108f3ed2be90a920f6bc4434))
* WIP: 唱票显示玩家标签 ([6e33b77](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6e33b77994a29a448caadc57b139317b32dfa46f))
* WIP: 动效 ([6973917](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/69739170802c168addd20de8b373cff5136cff61))
* WIP: 故事书 ([f3c3eba](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f3c3ebab2486eaa52c8969e285282a9ccc81ba5d))
* WIP: 故事书 ([8df1c29](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8df1c296ef8ad1c825b8282790bd659accbdedfd))
* WIP: 故事书 ([78b5453](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/78b5453fe22a46b0bfe3557441da86dc7cd7dbdf))
* WIP: 故事书 ([6feab5e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6feab5e1ead139d060850ec1b415423e4a9450e8))
* WIP: 故事书 ([3757dab](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/3757dabeb7db95bcdd5ba8e52c58c735ce00b837))
* WIP: 故事书 ([91da51f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/91da51fe85b1b18cb3c532eed0fc7fa188bf3549))
* WIP: 故事书 ([aee805f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/aee805f119ee4ea3e3a259e9dbc864c6d9bc77c8))
* WIP: 故事书 ([bffc1fa](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bffc1fa9e67893f23dac2d8b96a74f84ac28554b))
* WIP: 故事书 ([78d0803](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/78d08038cb9742a50ca42b0872060974de02afdb))
* WIP: 故事书 ([9710277](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9710277f85e833f1bd44d36b0704eb7e5b2bc0cf))
* WIP: 故事书 ([fec730a](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fec730a53a458ce927aa09396a12ab436780284a))
* WIP: 故事书 ([de70e91](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/de70e912069250c365381ebf8b31627815c8a155))
* WIP: 故事书 ([e2cbf1c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e2cbf1c5aa942a28fa41813af1c932e44484e05d))
* WIP: 故事书 ([bd89887](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bd898874914418be9b2a031dea0e5e08a362b805))
* WIP: 故事书-轮播切换优化 ([bfdc1c5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bfdc1c518b6604ad042aa64ca8b163dbe1178c25))
* WIP: 故事书-轮播切换优化 ([1aa95fa](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/1aa95faf91b483182267cabfaae6b037d26d252c))
* WIP: 故事书-使用proto中的类型定义 ([f8e158d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f8e158d5432b5e6697d94a7c128b17e1e290d054))
* WIP: 卡牌支持显示投票图章 ([865de5b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/865de5b3f9946fda22d741cd8a29b2bf92955e9a))
* WIP: 卡牌支持显示投票图章 ([5c0d182](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5c0d182109ba48d63ec085e75b2221c344e40509))
* WIP: 卡牌资源动态加载 ([5023b12](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5023b127f24251e6d5c849de6965e844e5e4c2df))
* WIP: 卡牌资源动态加载 ([670d2c6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/670d2c6102f5f859573ad3a07a6b34f783e415fd))
* WIP: 卡牌资源动态加载 ([f7c9472](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/f7c9472182a66556f40da36674c99accbbb86f9b))
* WIP: 卡牌资源动态加载 ([c09cee2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c09cee2cd9ccf41486ba0fc4f1ff1789f9ae16cd))
* WIP: 卡牌资源动态加载 ([07066bc](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/07066bcbf153f1efd52ba6e0881e07d6b2e6b93e))
* WIP: 卡牌资源动态加载 ([67d90a6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/67d90a64cc672636b95756aedb8bf7b9f9f8ebfa))
* WIP: 投票 ([e214acd](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e214acd9808f30b9fccd690d262010af5b23bfa1))
* WIP: 投票 ([a54eb24](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/a54eb2459b12ff7fed378ca8ee84d579500533de))
* WIP: 投票 ([bd9db14](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/bd9db1456123cec118aaadf927e3439fee38c6f0))
* WIP: 投票 ([9eb90b2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/9eb90b208c57b311e0a63afdcd1ae078ac363803))
* WIP: 投票 ([81c740d](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/81c740d3b748eca29962a1ea5c694f3fef5dee27))
* WIP: 投票 ([0538216](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0538216aa9b4f7c5145b556ae371f79818c01a28))
* WIP: 投票 ([c35b8c6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/c35b8c66c8512d0b5e8939184ca3725cae6b24c4))


### 🐛 Bug修复

* 报错 ([4d24c0c](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4d24c0cb19ef36f7fb8a08d47a8effb936d87d7f))
* 尝试修复装扮层级问题 ([06aacf6](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/06aacf62ce29715a5cb69f82ebaccc23e7192475))
* 尝试修复装扮层级问题 ([b41dd4b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/b41dd4b9f390928721b642bb62c91067dafb8b22))
* 尝试修复装扮层级问题 ([5ab7f84](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/5ab7f8490eb45b81e5af3c231d0b52a6b0821c59))
* 尝试修复装扮层级问题 ([0766ca2](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0766ca295f30aeb18fbf6927bacf2bbacbea45a2))
* 尝试修复装扮层级问题 ([d92ae1b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/d92ae1bc2592d4adf499456d8acf021a44d18af0))
* 尝试修复装扮层级问题 ([034781f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/034781fabfdc66096a59954b359d4d28207371af))
* 尝试修复装扮层级问题 ([0dd31f3](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0dd31f3ca35d8566a742addc473464933c1a1507))
* 尝试修复装扮层级问题 ([0fada1e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0fada1e8ae5a7d44cb20f8e5233c36576ed87d87))
* 尝试修复装扮层级问题 ([6d3401b](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/6d3401bb11e4ebc6eab83d2c91ab3d7e7df92ba0))
* 尝试修复装扮层级问题 ([fa1b7e4](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/fa1b7e49124e9671477184dd6eba482025b67fdd))
* 倒计时 ([536cf1f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/536cf1f2058a9f23cae5a83ef93538efd7b7eab2))
* 卡牌渲染时可能卡牌已经被销毁了 ([e94b7a9](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e94b7a97d8a8a4c2a9708ee08ef805d06e06af5d))
* 日志弹框关闭 ([ecf1da5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/ecf1da5ade49a7e912b17d6f267dc8f10cf63cee))
* 投票时快速点击切换卡牌 ([4428395](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/4428395bdf6af63460b1b1297f2a21646560583d))
* 修复版本管理配置中的路径重复问题 ([8e1fe4e](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/8e1fe4e88c4e2721fbc51741958ed636f6c092c8))
* 修复投票组件缩略模式布局 ([7de16e5](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/7de16e5ba363b4b74e7fbc8eb9adc582a764a56e))
* cardItem 层级问题 ([baf9b29](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/baf9b295d872dd120bc70eb56e71c07f43eb6cf0))
* debug环境判断去掉 local ([e69591f](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/e69591fc4859e4c97d0857007efb94155675aa5f))
* error ([0cf45cd](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/0cf45cd54c8a5941236c182d2b8881756684fe3b))
* error ([2472062](https://gitlab.stnts.com/cloudgame/bomb-duck/commit/2472062e8f80aca231913fc7a51070466dfb343f))

# 只言片语 - 更新日志

本项目的所有重要更改都将记录在此文件中。

本文件格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增
- 初始化版本管理系统
- 集成 standard-version 进行自动化版本管理

### 变更
- 无

### 修复
- 无

### 移除
- 无

---

*注意：此 CHANGELOG 将在首次运行 standard-version 后自动更新*
