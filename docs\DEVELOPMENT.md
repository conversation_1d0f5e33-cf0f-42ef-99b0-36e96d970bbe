# 🚀 开发环境配置

## 💻 本地开发环境

需要安装 Node.js，推荐使用 nvm 来安装，可以方便的在不同的 Node.js 版本之间切换，也可以直接安装 Node.js 的 22.11.0 版本。

### 📦 安装 nvm

-   **Mac/Linux**: https://github.com/nvm-sh/nvm
-   **Windows**: https://github.com/coreybutler/nvm-windows

### ⚡ 通过 nvm 安装 Node.js 22.11.0 版本

```bash
# 安装 Node.js 22.11.0 版本
nvm install 22.11.0
```

### 🛠️ 使用 npm 安装 yarn 和 husky 全局依赖

```bash
npm i yarn -g
npm i husky -g
```

### 🔑 到 GitLab 里面添加自己开发机器的 SSH Keys

> 如果之前已经添加过可以忽略该步骤

-   **配置地址**: https://gitlab.stnts.com/profile/keys

要添加 SSH key，您需要 [生成一个新的](https://gitlab.stnts.com/help/ssh/README#generating-a-new-ssh-key-pair) 或使用 [现有的密钥](https://gitlab.stnts.com/help/ssh/README#locating-an-existing-ssh-key-pair)。

### 📥 进入项目根目录，然后安装项目依赖

```bash
yarn install --ignore-optional
```

### 🎮 安装 Cocos Creator Dashboard

1. 下载并安装 Cocos Creator Dashboard
2. 通过 Dashboard 安装 **3.8.5** 版本的编辑器

**官方文档**: https://docs.cocos.com/creator/3.8/manual/zh/getting-started/install/

### 📂 导入项目

使用 Cocos Creator Dashboard 导入安装完依赖的项目，路径为 `packages/game-shark`

### 🎯 启动预览

在场景中选中 **start** 场景并点击预览，即可启动浏览器打开游戏进行本地开发预览

## 🌐 联调及测试环境

### ⚙️ 环境配置

**Hosts 配置** (测试路由器 `st-csb-suileyoo-testing` 已添加 hosts)

```bash
# 联调环境 hosts
********** cocos-game-dev.mityoo.com
*********** game-server-dev.mityoo.com

# 测试环境 hosts
********** cocos-game-testing.mityoo.com
```

### 🔗 访问地址

#### 🔧 联调环境访问地址

```bash
# 服务端创建对局
https://cocos-game-dev.mityoo.com/dashboard/#/

# 客户端游戏地址
https://cocos-game-dev.mityoo.com/shark/web-mobile/
```

#### 🧪 测试环境访问地址

```bash
# 客户端游戏地址
https://cocos-game-testing.mityoo.com/shark/web-mobile/
```

### 🚀 发布流程

项目采用 GitLab CI/CD 进行自动化构建和部署，支持两种发布方式：**Web 发布**和 **ZIP 资源包发布**。

#### 📋 CI/CD 流程概览

```mermaid
graph TD
    A[代码提交] --> B[install: 安装依赖]
    B --> C[build: 构建游戏]
    C --> D[build_zip: 生成ZIP包]

    D --> E1[upload_s3_dev: 上传到S3联调环境]
    D --> E2[upload_s3_test: 上传到S3测试环境]
    D --> E3[upload_s3_pre: 上传到S3预发布环境]
    D --> E4[upload_s3_prod: 上传到S3生产环境]

    C --> F1[deploy_dev: 部署到联调环境]
    C --> F2[deploy_test: 部署到测试环境]
	C --> F3[release: 方舟平台发布]

```

#### 🌐 Web 发布流程（直接部署）

**适用场景**: 联调环境和测试环境的快速部署

**流程步骤**:

1. **自动触发**: 当相关文件发生变更时，CI 自动运行构建流程
2. **构建阶段**:
    - 使用 Cocos Creator 3.8.5 构建 web-mobile 版本
    - 生成静态资源文件到 `packages/game-*/build` 目录
3. **部署阶段**:
    - **联调环境**: 手动触发 `deploy_dev_*` 任务部署到 `/webser/www/cocos-games-dev/`
    - **测试环境**: 手动触发 `deploy_test_*` 任务部署到 `/webser/www/cocos-games/`
    - **线上环境**: 手动触发 `release` 任务，通过方舟平台进行发布

**手动触发部署**:

```bash
# 在 GitLab CI/CD Pipeline 页面手动触发对应的部署任务
# 例如：deploy_test_shark, deploy_test_pirate, deploy_test_dixit 等
```

#### 📦 ZIP 资源包发布流程（S3 上传）

**适用场景**: 语音房集成、生产环境发布

**流程步骤**:

1. **构建 ZIP 包**:
    - 基于 `build_zip_*` 任务生成 web-mobile 资源包
    - 将构建产物复制到根目录的 `web-mobile/` 文件夹
2. **上传到 S3**:
    - 打包为 `{GAME_CODE}-{CI_COMMIT_SHORT_SHA}.zip` 格式
    - 上传到指定的 S3 存储桶：`games/{gameCode}/` 路径下
    - 支持分片上传（8MB 分片）和进度显示
3. **通知服务端**:
    - 调用服务端 API 通知新包地址
    - 传递游戏代码和包下载链接

**支持的环境**:

-   **联调环境** (`upload_s3_dev_*`): 开发联调使用
-   **测试环境** (`upload_s3_test_*`): 测试验证使用
-   **预发布环境** (`upload_s3_pre_*`): 预发布验证使用
-   **生产环境** (`upload_s3_prod_*`): 正式发布使用

**手动触发上传**:

```bash
# 在 GitLab CI/CD Pipeline 页面手动触发对应的上传任务
# 例如：upload_s3_test_shark, upload_s3_prod_dixit 等
```

#### 🎮 支持的游戏项目

| 游戏名称 | 游戏代码      | 构建任务                | 部署任务               | S3 上传任务               |
| -------- | ------------- | ----------------------- | ---------------------- | ------------------------- |
| 小心鲨手 | `care_shark`  | `job_build_shark`       | `deploy_*_shark`       | `upload_s3_*_shark`       |
| 海盗桶   | `pirate`      | `job_build_pirate`      | `deploy_*_pirate`      | `upload_s3_*_pirate`      |
| 只言片语 | `dixit`       | `job_build_game_dixit`  | `deploy_*_game_dixit`  | `upload_s3_*_dixit`       |
| 飞行棋   | `flyingchess` | `job_build_flyingchess` | `deploy_*_flyingchess` | `upload_s3_*_flyingchess` |

#### ⚙️ 环境变量配置

**S3 上传相关**:

```bash
S3_ACCESS_KEY_ID=xxx          # S3访问密钥ID
S3_SECRET_ACCESS_KEY=xxx      # S3秘密访问密钥
S3_ENDPOINT=xxx               # S3终端节点URL
S3_REGION=xxx                 # S3区域
S3_BUCKET=xxx                 # S3存储桶名称
NOTIFY_API_URL=xxx            # 服务端通知API地址
NOTIFY_API_TOKEN=xxx          # API认证令牌
```

#### 🔄 发布操作指南

1. **联调环境发布**:

    - 手动触发 `deploy_dev_*` 进行 Web 部署
    - 手动触发 `upload_s3_dev_*` 进行 ZIP 包上传

2. **测试环境发布**:

    - 手动触发 `deploy_test_*` 进行 Web 部署
    - 手动触发 `upload_s3_test_*` 进行 ZIP 包上传

3. **预发布环境发布**

    - 预发布没有 Web 环境
    - 手动触发 `upload_s3_pre_*` 进行 ZIP 包上传

4. **生产环境发布**:
    - 手动触发 `upload_s3_prod_*` 上传生产包
    - 通过方舟平台 `release` 任务进行最终发布

### 🎮 后台创建对局（以联调环境为例）

1. **打开创建对局页面**: https://cocos-game-dev.mityoo.com/dashboard/#/
2. **填写表单**: 点击右下角按钮弹出创建对局表单，需要填写下面的表单项，其它表单项保持默认即可

#### 📋 表单配置项

```yaml
Web服务器：
https://game-server-dev.mityoo.com/

连接器地址：
wss://game-server-dev.mityoo.com/ws/conn

预览地址：
# 如果想在本地调试，可以换成本地的 localhost + 端口的地址
https://cocos-game-dev.mityoo.com/shark/web-mobile/

游戏ID：
care_shark

游戏模式：
竞技模式-个人

游戏Feature：
suileyoo

队伍数量：
2-6

高级设置-safe_top（顶部安全区域高度）：
100

高级设置-safe_bottom（底部安全区域高度）：
750

高级设置-额外字段：
{
  "state_notify": "",
  "report_game": "",
  "ranking_settlement": ""
}
```

3. **创建对局**: 点击创建按钮
4. **加入对战**: 页面上会针对填写的队伍数量生成多个对战项目，点击加入对战右侧的复制按钮可以复制地址到浏览器打开参与对战

#### 🎯 通过高级设置定制初始牌堆和玩家初始手牌

小心鲨手和海盗桶游戏可以在高级设置中通过 摸牌区初始牌 和 玩家\*手牌 字段，输入@后选择卡牌，来定制初始牌堆和玩家手牌，方便测试特定的卡牌和对局逻辑

#### 提示

> 📝 **提示**: 本文档涵盖了完整的开发环境配置流程，如有问题请联系开发团队。
