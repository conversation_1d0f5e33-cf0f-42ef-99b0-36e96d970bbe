[{"__type__": "cc.Prefab", "_name": "HandCardDialog", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "HandCardDialog", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 116}, {"__id__": 118}, {"__id__": 120}], "_prefab": {"__id__": 122}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 32768, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 3}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "ac8d558d-cabb-4ef9-bb6e-cb5f2dfed402", "__expectedType__": "cc.Prefab"}, "fileId": "fbBdrnmSBMxYtuW6B/GMxZ", "instance": {"__id__": 4}, "targetOverrides": [{"__id__": 98}, {"__id__": 101}, {"__id__": 104}, {"__id__": 107}, {"__id__": 110}, {"__id__": 113}]}, {"__type__": "cc.PrefabInstance", "fileId": "0aXJHjR+RLppyoOnPtLn0r", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 5}, {"__id__": 7}, {"__id__": 8}, {"__id__": 9}, {"__id__": 10}, {"__id__": 11}, {"__id__": 13}, {"__id__": 15}, {"__id__": 17}, {"__id__": 19}, {"__id__": 21}, {"__id__": 23}, {"__id__": 25}, {"__id__": 27}, {"__id__": 29}, {"__id__": 31}, {"__id__": 33}, {"__id__": 35}, {"__id__": 37}, {"__id__": 39}, {"__id__": 41}, {"__id__": 43}, {"__id__": 45}, {"__id__": 47}, {"__id__": 49}, {"__id__": 51}, {"__id__": 53}, {"__id__": 55}, {"__id__": 57}, {"__id__": 59}, {"__id__": 61}, {"__id__": 63}, {"__id__": 65}, {"__id__": 67}, {"__id__": 69}, {"__id__": 71}, {"__id__": 72}, {"__id__": 73}, {"__id__": 75}, {"__id__": 77}, {"__id__": 79}, {"__id__": 81}, {"__id__": 83}, {"__id__": 85}, {"__id__": 87}, {"__id__": 89}, {"__id__": 91}, {"__id__": 93}, {"__id__": 95}], "removedComponents": [{"__id__": 97}]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_name"], "value": "HandCard"}, {"__type__": "cc.TargetInfo", "localID": ["fbBdrnmSBMxYtuW6B/GMxZ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 12}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["d4Jk2ONg1AcLLDuqkonq+7"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 14}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["b8TeJkGPdBs4e4B1Wh9XDf"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 16}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["33oQnCzWtIh5GwgDKJQEPA"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 18}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["e9VKG8QiBGl5usQHWB55zJ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 20}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["4d8QKrtGhHDpdxMRsmy/Mc", "c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 22}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["49KhNLKEpMqouLJ4qzq6pY", "c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 24}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["b04qfrbf9JwZrjpfA8gNKJ", "c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 26}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["e0R1Mj0WxMtZLuQ1x3AbtE", "c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 28}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["1eGAVAt19HG4UekPoPjdgd", "c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 30}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["a69UZjCwdGfr+1bzvygEE4", "c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 32}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["6fQSZiDMRPw4JQZTaIzVra"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 34}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["4d8QKrtGhHDpdxMRsmy/Mc", "d8ugyLtq5IvLNWUOpp02hU"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 36}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["4d8QKrtGhHDpdxMRsmy/Mc", "2fN4etumdNEpWXd3Q8wW2W"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 38}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["49KhNLKEpMqouLJ4qzq6pY", "d8ugyLtq5IvLNWUOpp02hU"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 40}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["49KhNLKEpMqouLJ4qzq6pY", "2fN4etumdNEpWXd3Q8wW2W"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 42}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["b04qfrbf9JwZrjpfA8gNKJ", "d8ugyLtq5IvLNWUOpp02hU"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 44}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["b04qfrbf9JwZrjpfA8gNKJ", "2fN4etumdNEpWXd3Q8wW2W"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 46}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["e0R1Mj0WxMtZLuQ1x3AbtE", "d8ugyLtq5IvLNWUOpp02hU"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 48}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["e0R1Mj0WxMtZLuQ1x3AbtE", "2fN4etumdNEpWXd3Q8wW2W"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 50}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["1eGAVAt19HG4UekPoPjdgd", "d8ugyLtq5IvLNWUOpp02hU"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 52}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["1eGAVAt19HG4UekPoPjdgd", "2fN4etumdNEpWXd3Q8wW2W"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 54}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["a69UZjCwdGfr+1bzvygEE4", "d8ugyLtq5IvLNWUOpp02hU"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 56}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["a69UZjCwdGfr+1bzvygEE4", "2fN4etumdNEpWXd3Q8wW2W"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 58}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["ebmlqi6CFO07eu46FYHwCv"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 60}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["14uE+RkNVIsKvHRYXpOfP1"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 62}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["61XVPsF/NOjotXWoHezYMe"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 64}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["bbBp3xVLZLIrsQpmA1Mosk"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 66}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["88h2/bTSVFPagkp7DlH11j"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 68}, "propertyPath": ["_layer"], "value": 32768}, {"__type__": "cc.TargetInfo", "localID": ["2a2vbJEftCrbIFvb9xBN2b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 70}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 1080, "height": 1920}}, {"__type__": "cc.TargetInfo", "localID": ["5cAotBxGtMXKBNbG7SINi1"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 12}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 14}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 74}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 115, "height": 155}}, {"__type__": "cc.TargetInfo", "localID": ["4d8QKrtGhHDpdxMRsmy/Mc", "a09Df5FjxFDJO8F3Lzt+27"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 76}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 115, "height": 155}}, {"__type__": "cc.TargetInfo", "localID": ["49KhNLKEpMqouLJ4qzq6pY", "a09Df5FjxFDJO8F3Lzt+27"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 78}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 115, "height": 155}}, {"__type__": "cc.TargetInfo", "localID": ["b04qfrbf9JwZrjpfA8gNKJ", "a09Df5FjxFDJO8F3Lzt+27"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 80}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 115, "height": 155}}, {"__type__": "cc.TargetInfo", "localID": ["e0R1Mj0WxMtZLuQ1x3AbtE", "a09Df5FjxFDJO8F3Lzt+27"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 82}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 115, "height": 155}}, {"__type__": "cc.TargetInfo", "localID": ["1eGAVAt19HG4UekPoPjdgd", "a09Df5FjxFDJO8F3Lzt+27"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 84}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 115, "height": 155}}, {"__type__": "cc.TargetInfo", "localID": ["a69UZjCwdGfr+1bzvygEE4", "a09Df5FjxFDJO8F3Lzt+27"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 86}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 115, "height": 155}}, {"__type__": "cc.TargetInfo", "localID": ["4d8QKrtGhHDpdxMRsmy/Mc", "0730hqFx1GCLozZ3kLZIyE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 88}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 115, "height": 155}}, {"__type__": "cc.TargetInfo", "localID": ["49KhNLKEpMqouLJ4qzq6pY", "0730hqFx1GCLozZ3kLZIyE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 90}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 115, "height": 155}}, {"__type__": "cc.TargetInfo", "localID": ["b04qfrbf9JwZrjpfA8gNKJ", "0730hqFx1GCLozZ3kLZIyE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 92}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 115, "height": 155}}, {"__type__": "cc.TargetInfo", "localID": ["e0R1Mj0WxMtZLuQ1x3AbtE", "0730hqFx1GCLozZ3kLZIyE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 94}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 115, "height": 155}}, {"__type__": "cc.TargetInfo", "localID": ["1eGAVAt19HG4UekPoPjdgd", "0730hqFx1GCLozZ3kLZIyE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 96}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 115, "height": 155}}, {"__type__": "cc.TargetInfo", "localID": ["a69UZjCwdGfr+1bzvygEE4", "0730hqFx1GCLozZ3kLZIyE"]}, {"__type__": "cc.TargetInfo", "localID": ["d2J73gXOZAhbRJZX783as3"]}, {"__type__": "cc.TargetOverrideInfo", "source": null, "sourceInfo": null, "propertyPath": ["hand_card_array", "0"], "target": {"__id__": 99}, "targetInfo": {"__id__": 100}}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.TargetInfo", "localID": ["52Fr4S+npNhKj5uteDFjuM"]}, {"__type__": "cc.TargetOverrideInfo", "source": null, "sourceInfo": null, "propertyPath": ["hand_card_array", "1"], "target": {"__id__": 102}, "targetInfo": {"__id__": 103}}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.TargetInfo", "localID": ["52Fr4S+npNhKj5uteDFjuM"]}, {"__type__": "cc.TargetOverrideInfo", "source": null, "sourceInfo": null, "propertyPath": ["hand_card_array", "2"], "target": {"__id__": 105}, "targetInfo": {"__id__": 106}}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.TargetInfo", "localID": ["52Fr4S+npNhKj5uteDFjuM"]}, {"__type__": "cc.TargetOverrideInfo", "source": null, "sourceInfo": null, "propertyPath": ["hand_card_array", "3"], "target": {"__id__": 108}, "targetInfo": {"__id__": 109}}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.TargetInfo", "localID": ["52Fr4S+npNhKj5uteDFjuM"]}, {"__type__": "cc.TargetOverrideInfo", "source": null, "sourceInfo": null, "propertyPath": ["hand_card_array", "4"], "target": {"__id__": 111}, "targetInfo": {"__id__": 112}}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.TargetInfo", "localID": ["52Fr4S+npNhKj5uteDFjuM"]}, {"__type__": "cc.TargetOverrideInfo", "source": null, "sourceInfo": null, "propertyPath": ["hand_card_array", "5"], "target": {"__id__": 114}, "targetInfo": {"__id__": 115}}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.TargetInfo", "localID": ["52Fr4S+npNhKj5uteDFjuM"]}, {"__type__": "01db8gXjP1DDbI9fy5wbPAN", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 117}, "handCard": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "89a1/h67pNNYCpRXWV24VI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 119}, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fd5Y36olRFuar/QZnrQv/n"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 121}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1080, "_originalHeight": 1920, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1f/hc3qrpEKYVz7XdrTnNG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": [{"__id__": 123}, {"__id__": 125}, {"__id__": 128}, {"__id__": 131}, {"__id__": 134}, {"__id__": 137}, {"__id__": 140}, {"__id__": 143}], "nestedPrefabInstanceRoots": [{"__id__": 2}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 116}, "sourceInfo": null, "propertyPath": ["handCard"], "target": {"__id__": 2}, "targetInfo": {"__id__": 124}}, {"__type__": "cc.TargetInfo", "localID": ["55J9hEJWhNTKLYqNvVnDYC"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 126}, "propertyPath": ["_target"], "target": {"__id__": 2}, "targetInfo": {"__id__": 127}}, {"__type__": "cc.TargetInfo", "localID": ["efC3+hJkVA5oU0xDFxgL47"]}, {"__type__": "cc.TargetInfo", "localID": ["b88yw6jn1C4Y7gKAadjTp8"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 129}, "propertyPath": ["_target"], "target": {"__id__": 2}, "targetInfo": {"__id__": 130}}, {"__type__": "cc.TargetInfo", "localID": ["4d8QKrtGhHDpdxMRsmy/Mc", "a9tBNBYutNA56ViuavFNhK"]}, {"__type__": "cc.TargetInfo", "localID": ["4d8QKrtGhHDpdxMRsmy/Mc", "20vot2w+xKTbsIUjtYz2D0"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 132}, "propertyPath": ["_target"], "target": {"__id__": 2}, "targetInfo": {"__id__": 133}}, {"__type__": "cc.TargetInfo", "localID": ["49KhNLKEpMqouLJ4qzq6pY", "a9tBNBYutNA56ViuavFNhK"]}, {"__type__": "cc.TargetInfo", "localID": ["49KhNLKEpMqouLJ4qzq6pY", "20vot2w+xKTbsIUjtYz2D0"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 135}, "propertyPath": ["_target"], "target": {"__id__": 2}, "targetInfo": {"__id__": 136}}, {"__type__": "cc.TargetInfo", "localID": ["b04qfrbf9JwZrjpfA8gNKJ", "a9tBNBYutNA56ViuavFNhK"]}, {"__type__": "cc.TargetInfo", "localID": ["b04qfrbf9JwZrjpfA8gNKJ", "20vot2w+xKTbsIUjtYz2D0"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 138}, "propertyPath": ["_target"], "target": {"__id__": 2}, "targetInfo": {"__id__": 139}}, {"__type__": "cc.TargetInfo", "localID": ["e0R1Mj0WxMtZLuQ1x3AbtE", "a9tBNBYutNA56ViuavFNhK"]}, {"__type__": "cc.TargetInfo", "localID": ["e0R1Mj0WxMtZLuQ1x3AbtE", "20vot2w+xKTbsIUjtYz2D0"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 141}, "propertyPath": ["_target"], "target": {"__id__": 2}, "targetInfo": {"__id__": 142}}, {"__type__": "cc.TargetInfo", "localID": ["1eGAVAt19HG4UekPoPjdgd", "a9tBNBYutNA56ViuavFNhK"]}, {"__type__": "cc.TargetInfo", "localID": ["1eGAVAt19HG4UekPoPjdgd", "20vot2w+xKTbsIUjtYz2D0"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 144}, "propertyPath": ["_target"], "target": {"__id__": 2}, "targetInfo": {"__id__": 145}}, {"__type__": "cc.TargetInfo", "localID": ["a69UZjCwdGfr+1bzvygEE4", "a9tBNBYutNA56ViuavFNhK"]}, {"__type__": "cc.TargetInfo", "localID": ["a69UZjCwdGfr+1bzvygEE4", "20vot2w+xKTbsIUjtYz2D0"]}]